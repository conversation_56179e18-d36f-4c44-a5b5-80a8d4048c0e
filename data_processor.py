"""
数据处理和分析模块
"""
import pandas as pd
from typing import List, Dict, Any
from loguru import logger

from models import Campaign, SimilarityData, FinalResult
from wb_api_client import WildberriesAPIClient
from database import db_manager


class KeywordAnalyzer:
    """关键词分析器"""
    
    def __init__(self, api_client: WildberriesAPIClient):
        self.api_client = api_client
    
    def analyze_campaign(self, campaign: Campaign):
        """分析单个广告活动"""
        logger.info(f"开始分析广告活动 {campaign.campaign_id}")

        try:
            # 1. 获取关键词
            keywords = self.api_client.get_campaign_keywords(campaign.campaign_id)
            if not keywords:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有关键词")
                return pd.DataFrame()

            logger.info(f"获取到 {len(keywords)} 个关键词")

            # 测试输出keywords数据
            logger.info("=== Keywords 数据测试 ===")
            for i, keyword in enumerate(keywords[:3]):  # 只显示前3个
                logger.info(f"Keyword {i+1}: {keyword}")

            # 2. 获取关键词统计数据
            keyword_stats_dict = self.api_client.get_keyword_datas_monthly(campaign.campaign_id)
            logger.info(f"获取到 {len(keyword_stats_dict)} 个关键词的统计数据")

            # 测试输出keyword_stats_dict数据
            logger.info("=== Keyword Stats Dict 数据测试 ===")
            for i, (key, value) in enumerate(list(keyword_stats_dict.items())[:3]):  # 只显示前3个
                logger.info(f"Stats {i+1}: Key={key}, Value={value}")

            # 3. 获取产品列表
            products = self.api_client.get_campaign_products(campaign.campaign_id)
            if not products:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有产品")
                return pd.DataFrame()

            logger.info(f"获取到 {len(products)} 个产品")

            # 4. 批量获取数据库中相似度数据
            keyword_product_pairs = []
            for keyword in keywords:
                for product in products:
                    keyword_product_pairs.append((keyword.keyword, product.nm_id))

            logger.info(f"准备查询 {len(keyword_product_pairs)} 个关键词-产品组合的相似度数据")
            similarity_data_dict = db_manager.batch_get_similarity_data(keyword_product_pairs)
            logger.info(f"从数据库获取到 {len(similarity_data_dict)} 条相似度数据")

            # 测试输出similarity_data_dict数据
            logger.info("=== Similarity Data Dict 数据测试 ===")
            for i, (key, value) in enumerate(list(similarity_data_dict.items())[:3]):  # 只显示前3个
                logger.info(f"Similarity {i+1}: Key={key}, Value={value}")

            # 5. 使用pandas进行数据拼接
            return self._merge_data_with_pandas(campaign.campaign_id, keywords, keyword_stats_dict, products, similarity_data_dict)

        except Exception as e:
            logger.error(f"分析广告活动 {campaign.campaign_id} 失败: {e}")
            return pd.DataFrame()

    def _merge_data_with_pandas(self, campaign_id: int, keywords, keyword_stats_dict, products, similarity_data_dict):
        """使用pandas合并数据"""
        logger.info("开始使用pandas合并数据...")

        # 1. 创建关键词DataFrame
        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'keyword': keyword.keyword,
                'count': keyword.count
            })
        df_keywords = pd.DataFrame(keywords_data)
        logger.info(f"关键词DataFrame: {len(df_keywords)} 行")

        # 2. 创建关键词统计DataFrame
        stats_data = []
        for keyword, stats in keyword_stats_dict.items():
            stats_data.append({
                'keyword': keyword,
                'views': stats.get('views', 0),
                'sum': stats.get('sum', 0.0),
                'clicks': stats.get('clicks', 0),
                'ctr': stats.get('ctr', 0.0)
            })
        df_stats = pd.DataFrame(stats_data)
        logger.info(f"统计数据DataFrame: {len(df_stats)} 行")

        # 3. 创建相似度DataFrame
        similarity_data = []
        for (keyword, nm_id), sim_data in similarity_data_dict.items():
            similarity_data.append({
                'keyword': keyword,
                'nm_id': nm_id,
                'avg_similarity': sim_data.avg_similarity,
                'similar_count': sim_data.similar_count,
                'competitor_count': sim_data.competitor_count,
                'valid_scores': sim_data.valid_scores
            })
        df_similarity = pd.DataFrame(similarity_data)
        logger.info(f"相似度DataFrame: {len(df_similarity)} 行")

        # 4. 创建产品DataFrame（为了完整性）
        products_data = []
        for product in products:
            products_data.append({
                'nm_id': product.nm_id
            })
        df_products = pd.DataFrame(products_data)
        logger.info(f"产品DataFrame: {len(df_products)} 行")

        # 5. 进行数据合并（类似Excel的VLOOKUP）
        # 首先合并关键词和统计数据
        df_merged = df_keywords.merge(df_stats, on='keyword', how='left')
        logger.info(f"合并关键词和统计数据后: {len(df_merged)} 行")

        # 创建关键词-产品的笛卡尔积
        df_keyword_product = df_merged.assign(key=1).merge(df_products.assign(key=1), on='key').drop('key', axis=1)
        logger.info(f"创建关键词-产品组合后: {len(df_keyword_product)} 行")

        # 合并相似度数据
        df_final = df_keyword_product.merge(df_similarity, on=['keyword', 'nm_id'], how='left')
        logger.info(f"合并相似度数据后: {len(df_final)} 行")

        # 6. 添加广告ID列
        df_final['campaign_id'] = campaign_id

        # 7. 重新排列列顺序，确保字段完整性
        expected_columns = ['campaign_id', 'nm_id', 'keyword', 'avg_similarity', 'similar_count',
                          'competitor_count', 'valid_scores', 'views', 'sum', 'clicks', 'ctr', 'count']

        # 确保所有列都存在，如果不存在则填充空值
        for col in expected_columns:
            if col not in df_final.columns:
                df_final[col] = None

        # 选择并重新排序列
        df_final = df_final[expected_columns]

        logger.info(f"最终DataFrame: {len(df_final)} 行, {len(df_final.columns)} 列")
        logger.info(f"列名: {list(df_final.columns)}")

        # 检查字段完整性
        logger.info("=== 字段完整性检查 ===")
        for col in expected_columns:
            non_null_count = df_final[col].notna().sum()
            null_count = df_final[col].isna().sum()
            logger.info(f"{col}: 非空 {non_null_count}, 空值 {null_count}")

        return df_final
    
    def analyze_multiple_campaigns(self, campaigns: List[Campaign]) -> List[FinalResult]:
        """分析多个广告活动"""
        all_results = []
        
        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"处理第 {i}/{len(campaigns)} 个广告活动: {campaign.campaign_id}")
            
            try:
                results = self.analyze_campaign(campaign)
                all_results.extend(results)
            except Exception as e:
                logger.error(f"处理广告活动 {campaign.campaign_id} 时发生错误: {e}")
                continue
        
        logger.info(f"所有广告活动分析完成，总共生成 {len(all_results)} 条结果")
        return all_results




